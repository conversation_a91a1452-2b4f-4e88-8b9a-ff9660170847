/*
 * Requisition Table
 * Table for displaying requisition information with queue management
 * Uses Table with Queue mixin to prevent WET-BOEW conflicts on multi-table pages
 */
import { Table } from '../../core/components/table.js';
import { withQueue } from '../../core/components/table-features.js';
import { RequisitionApi } from '../../core/services/requisition-api.js';

// Requisition table configuration
const REQUISITION_TABLE_CONFIG = {
    entityTerms: {
        en: 'requisitions',
        fr: 'demandes'
    },
    columns: [
        {
            key: 'reqName',
            header: { en: 'Requisition', fr: 'Demande' },
            field: 'req_name'
        },
        {
            key: 'lab',
            header: { en: 'Lab', fr: 'Laboratoire' },
            field: 'lab_name',
            showForRole: 'scientist'
        },
        {
            key: 'dateModified',
            header: { en: 'Date Modified', fr: 'Date de modification' },
            accessor: (row) => row.updated_at || row.created_at,
            formatter: 'formatDate'
        }
    ],
    tableOptions: {
        order: [[2, "desc"]] // Sort by date modified descending
    }
};

class RequisitionTable extends withQueue(Table) {
    constructor(options = {}) {
        super({
            containerId: options.containerId || 'requisition-table-container',
            tableId: options.tableId || 'requisition-table',
            entityTerms: REQUISITION_TABLE_CONFIG.entityTerms,
            columns: REQUISITION_TABLE_CONFIG.columns,
            tableOptions: REQUISITION_TABLE_CONFIG.tableOptions,
            ...options
        });
    }

    // Fetch requisitions data from API
    async fetchData() {
        try {
            const config = this.currentConfig;
            const requisitions = await RequisitionApi.getRequisitions(config.apiStatusQuery, config.labId, config.role);

            // Backend provides data sorted by timestamp (newest first)
            return requisitions || [];
        } catch (error) {
            console.error('Failed to fetch requisitions data:', error);
            throw error;
        }
    }

}

// Factory function and default instance
export const createRequisitionTable = (options = {}) => new RequisitionTable(options);
export const requisitionTable = createRequisitionTable();

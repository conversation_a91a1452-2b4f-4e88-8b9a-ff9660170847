/*
 * View Requisitions Page JavaScript
 * Simplified version - all roles see both open and closed requisitions
 */
import { REQUISITION_CONFIGS } from '../../core/config/requisition-configs.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { createRequisitionTable } from './requisition-table.js';
import { getLabId } from '../lab/shared/lab-helpers.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';

// Create config maps for message helper
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS
};

// Configure page authentication requirements
window.PAGE_AUTH_CONFIG = {
    onAuthSuccess: initializeViewRequisitionsPage
};

// Initialize view requisitions page
async function initializeViewRequisitionsPage(userInfo) {
    try {
        // Check lab assignment for lab staff roles
        if ((userInfo.role === GLOBAL_CONFIGS.application.roles.LAB_ADMIN || 
             userInfo.role === GLOBAL_CONFIGS.application.roles.LAB_PERSONNEL) && 
            !getLabId(userInfo)) {
            showMessage('#page-error-container', 'auth.messages.errors', 'missingLabAssignment', MESSAGE_CONFIG_MAPS);
            return;
        }

        // Create both tables - all roles see both open and closed requisitions
        const openTable = createRequisitionTable();
        const closedTable = createRequisitionTable();

        // Show both sections
        $('#open-requisitions-section').prop('hidden', false).attr('aria-hidden', 'false');
        $('#closed-requisitions-section').prop('hidden', false).attr('aria-hidden', 'false');

        // Create table configurations
        const baseConfig = {
            role: userInfo.role,
            labId: getLabId(userInfo)
        };

        // Create both tables concurrently
        await Promise.all([
            openTable.create({
                ...REQUISITION_CONFIGS.ui.requisitionTable.queues.open,
                ...baseConfig
            }),
            closedTable.create({
                ...REQUISITION_CONFIGS.ui.requisitionTable.queues.closed,
                ...baseConfig
            })
        ]);

        console.log('Both requisition tables initialized successfully');
    } catch (error) {
        console.error('View requisitions page initialization failed:', error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

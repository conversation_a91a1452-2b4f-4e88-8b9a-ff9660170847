/*
 * Requisition Configuration
 *
 * 1. FUNCTIONAL SETTINGS - Global technical configurations (form lengths, navigation delays, etc.)
 * 2. UI TEXT - Global UI text with bilingual support (common buttons, navigation, etc.)
 * 3. MESSAGES - Cross-cutting messages organized by type and category
 * 
 */

export const REQUISITION_CONFIGS = {
    // ===== 1. FUNCTIONAL SETTINGS =====
    // Requisition-related technical configurations
    // Add functional settings here as needed (form validation rules, API settings, etc.)

    // ===== 2. UI TEXT (Bilingual) =====
    // Requisition UI text organized by component/feature
    ui: {
        // Requisition table configurations
        requisitionTable: {
            containerId: 'requisition-table-container',
            tableId: 'requisition-table',
            entityTerms: {
                en: 'requisitions',
                fr: 'demandes'
            },
            // Queue-specific configurations
            queues: {
                open: {
                    containerId: 'open-requisitions-table-container',
                    tableId: 'open-requisitions-table',
                    title: {
                        en: 'Open Requisitions',
                        fr: 'Demandes ouvertes'
                    },
                    apiStatusQuery: 'submitted&status=in_progress'
                },
                closed: {
                    containerId: 'closed-requisitions-table-container',
                    tableId: 'closed-requisitions-table',
                    title: {
                        en: 'Closed Requisitions',
                        fr: 'Demandes fermées'
                    },
                    apiStatusQuery: 'complete'
                }
            },
            // Table configuration
            columns: [
                {
                    key: 'reqName',
                    header: { en: 'Requisition', fr: 'Demande' },
                    field: 'req_name' 
                },
                {
                    key: 'lab',
                    header: { en: 'Lab', fr: 'Laboratoire' },
                    field: 'lab_name',
                    showForRole: 'scientist'
                },
                {
                    key: 'dateModified',
                    header: { en: 'Date Modified', fr: 'Date de modification' },
                    accessor: (row) => row.updated_at || row.created_at,
                    formatter: 'formatDate'
                }
            ],
            tableOptions: {
                order: [[2, "desc"]] // Sort by date modified descending
            }
        }

        // Add more UI sections here as needed (forms, navigation, etc.)
    },

    // ===== 3. MESSAGES (Bilingual) =====
    // Requisition-specific messages use global templates
    // Most messages use global templates with entity substitution:
    // - CRUD operations: global.messages.success/errors.templates.entity* (uses {entity} placeholder)
    // - Connection errors: global.messages.errors.server.connectionError
};

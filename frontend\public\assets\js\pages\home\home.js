/*
 * Home Page JavaScript
 * Handles initialization of requisition tables based on user role
 */
import { REQUISITION_CONFIGS } from '../../core/config/requisition-configs.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { createRequisitionTable } from '../requisition/requisition-table.js';
import { getLabId, hasLabAssignment } from '../lab/shared/lab-helpers.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';

// Create config maps for message helper
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS
};

// Configure page authentication requirements
// This will be processed by authentication.js middleware
window.PAGE_AUTH_CONFIG = {
    onAuthSuccess: initializeHomePage
};

// Initialize home page
async function initializeHomePage(userInfo) {
    try {
        await initializeRequisitionTables(userInfo);
    } catch (error) {
        console.error('Home page initialization failed:', error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

// Initialize requisition tables based on user role
async function initializeRequisitionTables(userInfo) {
    const baseConfig = {
        role: userInfo.role,
        labId: getLabId(userInfo)
    };

    // Different logic for different roles
    if (userInfo.role === GLOBAL_CONFIGS.application.roles.SCIENTIST) {
        await initializeBothTables(baseConfig);
    } else if (userInfo.role === GLOBAL_CONFIGS.application.roles.LAB_ADMIN || 
               userInfo.role === GLOBAL_CONFIGS.application.roles.LAB_PERSONNEL) {
        await initializeLabStaffTables(baseConfig);
    }
}

// Initialize both tables for scientists
async function initializeBothTables(baseConfig) {
    const $openSection = $('#open-requisitions-section');
    const $closedSection = $('#closed-requisitions-section');

    if ($openSection.length && $closedSection.length) {
        // Show both sections
        $openSection.prop('hidden', false).attr('aria-hidden', 'false');
        $closedSection.prop('hidden', false).attr('aria-hidden', 'false');

        // Create tables concurrently
        const openTable = createRequisitionTable();
        const closedTable = createRequisitionTable();

        await Promise.all([
            openTable.create({
                ...REQUISITION_CONFIGS.ui.requisitionTable.queues.open,
                ...baseConfig
            }),
            closedTable.create({
                ...REQUISITION_CONFIGS.ui.requisitionTable.queues.closed,
                ...baseConfig
            })
        ]);

        console.log('Both requisition tables initialized successfully');
    }
}

// Initialize tables for lab staff roles
async function initializeLabStaffTables(baseConfig) {
    if (!baseConfig.labId) {
        showMessage('#page-error-container', 'auth.messages.errors', 'missingLabAssignment', MESSAGE_CONFIG_MAPS);
        return;
    }

    const $openSection = $('#open-requisitions-section');
    if ($openSection.length) {
        $openSection.prop('hidden', false).attr('aria-hidden', 'false');

        const openTable = createRequisitionTable();
        await openTable.create({
            ...REQUISITION_CONFIGS.ui.requisitionTable.queues.open,
            ...baseConfig
        });
    }
}